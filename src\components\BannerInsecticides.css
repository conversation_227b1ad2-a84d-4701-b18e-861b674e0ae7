/* 🌟 Shop Banner */
.shop-banner4 {
  position: relative;
  width: 100vw;
  min-height: 40vh;
  background: url("../assets/images/categ2.png") center/cover no-repeat fixed !important;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

/* 🔥 Overlay Effect */
.overlayy4 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5); /* Black overlay with 50% opacity */
  z-index: 1; /* Keeps overlay above background */
}

/* 🌟 Content Styling */
.contentt {
  position: relative;
  z-index: 2; /* Ensures text appears above the overlay */
  color: white;
  text-align: center;
}

/* 🌟 Subheading */
.subheading {
  color: #f3c258; /* Golden color */
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 0.5rem;
  letter-spacing: 1px;
}

/* 🌟 Main Title */
.title {
  font-size: 30px;
  font-weight: bold;
  font-family: "Poppins", sans-serif;
  text-transform: uppercase;
  margin-bottom: 0.5rem;
  letter-spacing: 2px;
}

/* 🌟 Yellow Underline */
.underline {
  width: 100px;
  height: 5px;
  background-color: #f3c258;
  margin: 10px auto;
  border-radius: 2px;
}

/* 🌟 Breadcrumb Navigation */
.breadcrumb-container {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
}
/* 🌟 Breadcrumb Navigation Links */
.breadcrumb-link {
  color: white;
  text-decoration: none;
  font-weight: bold;
  transition: color 0.3s;
  margin: 0 0.2rem;
}

.breadcrumb-link:hover {
  color: #18a728; /* Golden color on hover */
}
