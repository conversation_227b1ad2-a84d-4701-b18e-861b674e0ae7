/* Organic Section Styles */
.organic-section {
    background-color: #ffffff;
    padding: 50px 10px;
  }
  
  /* Image Container */
  .image-container {
    position: relative;
    display: inline-block;
    animation: scaleIn 1s ease-in-out;
  }
  
  /* Scale-in animation */
  @keyframes scaleIn {
    0% {
      transform: scale(0.8);
      opacity: 0;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }
  
  /* Golden Square Positioned Behind the Image */
  .background-square {
    width: 280px;
    height: 280px;
    border: 12px solid #f3c258;
    position: absolute;
    top: 40%;
    left: 40%;
    transform: translate(-50%, -50%);
    z-index: 0;
    animation: fadeIn 1s ease-in-out;
  }
  
  /* Fade-in animation */
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  /* Product Image */
  .product-image {
    max-width: 100%;
    height: auto;
    position: relative;
    z-index: 1;
  }
  
  /* Circular Progress Bars */
  .progress-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 10px solid #28a745;
    border-left-color: #eaeaea;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: bold;
    color: #f3c258;
    position: relative;
    animation: rotateProgress 4s linear infinite;
    -webkit-animation: rotateProgress 4s linear infinite;
}
  
  /* Rotating animation for progress circles */
  @keyframes rotateProgress {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  /* Percentage Text Inside Circle */
  .progress-text {
    position: absolute;
    font-size: 18px;
    font-weight: bold;
  }
  
  /* Slide-up effect for text */
  h3, p {
    animation: slideUp 1s ease-in-out;
  }
  
  @keyframes slideUp {
    0% {
      transform: translateY(20px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }
  