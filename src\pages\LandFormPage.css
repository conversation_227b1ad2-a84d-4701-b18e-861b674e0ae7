.landformpage-container {
  min-height: 100vh;
  background: linear-gradient(120deg, #e8f5e9 0%, #f5f7fa 100%);
  padding: 32px 0;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.sidebar {
  height: auto;
  background: #fff;
  box-shadow: 2px 0 12px rgba(0, 0, 0, 0.06);
  position: relative;
  top: auto;
  left: auto;
  padding-top: 20px;
  overflow-y: visible;

}

.sidebar-content {
  min-height: auto;
  padding: 0 24px 24px 24px;
}

.land-item {
  transition: background-color 0.3s ease;
}

.land-item:hover {
  background-color: #d4edda;
}

.form-container {
  margin-left: 0;
  min-height: 350px;
  background: #fff;
  padding: 40px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  border: 1px solid #28a745;
  transition: box-shadow 0.3s ease;
}

.form-container:hover {
  box-shadow: 0 0 25px rgba(40, 167, 69, 0.5);
}

.form-label {
  font-weight: 600;
  color: #2e7d32;
}

.form-control,
.form-select {
  border-radius: 8px;
  border: 1px solid #28a745;
  transition: border-color 0.3s ease;
}

.form-control:focus,
.form-select:focus {
  border-color: #1b5e20;
  box-shadow: 0 0 8px rgba(27, 94, 32, 0.5);
  outline: none;
}

.btn-success {
  background-color: #2e7d32;
  border-color: #2e7d32;
  transition: background-color 0.3s ease;
}

.btn-success:hover,
.btn-success:focus {
  background-color: #1b5e20;
  border-color: #1b5e20;
  box-shadow: 0 0 10px rgba(27, 94, 32, 0.7);
  outline: none;
}

@media (min-width: 992px) {
  .sidebar {
    margin-bottom: 20px;
  }
}

@media (max-width: 991.98px) {
  .sidebar {
    position: relative;
    height: auto;
    padding-top: 20px;
  }

  .form-container {
    margin-left: 0;
    margin-top: 20px;
  }
}
